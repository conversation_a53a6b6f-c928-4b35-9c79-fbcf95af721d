package com.haoxueren.widget;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.os.Vibrator;
import android.util.AttributeSet;
import android.util.Log;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.haoxueren.helper.PageLoadHelper;
import com.haoxueren.pray.bean.HaoPray;
import com.haoxueren.pray.R;
import com.haoxueren.pray.main.MainPresenter;
import com.haoxueren.proxy.RecyclerViewProxy;
import com.haoxueren.proxy.SuperViewHolder;
import com.scwang.smartrefresh.layout.SmartRefreshLayout;
import com.scwang.smartrefresh.layout.constant.RefreshState;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.function.Consumer;

@SuppressLint("CheckResult")
public class PrayRecordWidget extends FrameLayout {

    private static final String TAG = "PrayRecordWidget";

    FrameLayout containerLayout;
    SmartRefreshLayout refreshLayout;
    RecyclerViewProxy<HaoPray> recyclerView;
    View emptyStateView;

    private String prayId = "";
    private final int size = 20;
    private List<HaoPray> recordList = new ArrayList<>();
    private MainPresenter presenter = MainPresenter.getInstance();
    private PageLoadHelper<HaoPray> loadHelper = new PageLoadHelper<>(recordList, size);

    public PrayRecordWidget(@NonNull Context context) {
        this(context, null);
    }

    public PrayRecordWidget(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        Log.d(TAG, "PrayRecordWidget constructor called");

        View.inflate(context, R.layout.widget_pray_record, this);
        containerLayout = findViewById(R.id.containerLayout);
        refreshLayout = findViewById(R.id.refreshLayout);
        Log.d(TAG, "ContainerLayout initialized: " + (containerLayout != null));
        Log.d(TAG, "SmartRefreshLayout initialized: " + (refreshLayout != null));

        // 初始化空状态视图
        try {
            emptyStateView = View.inflate(context, R.layout.layout_pray_record_empty, null);
            Log.d(TAG, "Empty state view created successfully: " + (emptyStateView != null));
            if (emptyStateView != null) {
                Log.d(TAG, "Empty state view class: " + emptyStateView.getClass().getSimpleName());
                Log.d(TAG, "Empty state view initial visibility: " + emptyStateView.getVisibility());
            }
        } catch (Exception e) {
            Log.e(TAG, "Failed to create empty state view", e);
        }

        recyclerView = new RecyclerViewProxy<HaoPray>(this, R.id.recyclerView) {
            @Override
            protected SuperViewHolder<HaoPray> onCreateHolder(ViewGroup parent, int viewType) {
                return new SuperViewHolder<HaoPray>(parent, R.layout.item_pray_record) {

                    TextView recordTextView;

                    @Override
                    public void initView(View layout) {
                        recordTextView = itemView.findViewById(R.id.recordTextView);

                        // 添加长按监听器显示详情对话框
                        recordTextView.setOnLongClickListener(v -> {
                            int position = getBindingAdapterPosition();
                            if (position != -1 && position < getAdapterList().size()) {
                                HaoPray record = getAdapterList().get(position);

                                // 添加震动反馈
                                try {
                                    Vibrator vibrator = (Vibrator) getContext().getSystemService(Context.VIBRATOR_SERVICE);
                                    if (vibrator != null && vibrator.hasVibrator()) {
                                        vibrator.vibrate(50); // 震动50毫秒
                                    }
                                } catch (Exception e) {
                                    // 忽略震动错误
                                }

                                // 显示详情对话框
                                PrayRecordDetailDialog.show(getContext(), record);
                            }
                            return true; // 返回true表示消费了长按事件
                        });
                    }

                    @Override
                    public void updateItem(HaoPray bean) {
                        String record = String.format(Locale.CHINESE, "%s.%d %s", bean.getDate(), bean.getCount(), bean.getPray());
                        recordTextView.setText(record);
                    }
                };
            }
        };
        recyclerView.setAdapter(recordList);
        Log.d(TAG, "RecyclerView initialized with adapter, initial recordList size: " + recordList.size());

        initRefreshLayout();
        Log.d(TAG, "PrayRecordWidget initialization completed");
    }

    private void initRefreshLayout() {
        Log.d(TAG, "Initializing refresh layout");
        refreshLayout.setOnRefreshListener(layout -> {
            Log.d(TAG, "Refresh triggered, current prayId: '" + prayId + "'");
            // 确保只刷新当前分组的数据
            if (prayId != null && !prayId.isEmpty()) {
                Log.d(TAG, "Starting refresh for prayId: " + prayId);
                loadHelper.onRefresh((page, size) -> {
                    Log.d(TAG, "Refresh callback: loading record for prayId=" + prayId + ", skip=0");
                    loadRecord(prayId, 0);
                });
            } else {
                // 如果没有当前分组ID，直接完成刷新
                Log.w(TAG, "No prayId available, finishing refresh without loading data");
                refreshLayout.finishRefresh();
            }
        });
        refreshLayout.setOnLoadMoreListener(layout -> {
            Log.d(TAG, "Load more triggered, current prayId: '" + prayId + "', hasMoreData: " + loadHelper.hasMoreData());
            // 确保只加载当前分组的更多数据
            if (prayId != null && !prayId.isEmpty() && loadHelper.hasMoreData()) {
                loadHelper.onLoadMore((page, size) -> {
                    int skip = (page - 1) * size;
                    Log.d(TAG, "Load more callback: loading record for prayId=" + prayId + ", skip=" + skip);
                    loadRecord(prayId, skip);
                });
            } else {
                Log.d(TAG, "Load more conditions not met, finishing with no more data");
                refreshLayout.finishLoadMoreWithNoMoreData();
            }
        });
        Log.d(TAG, "Refresh layout initialization completed");
    }

    public void loadRecord(String id, int skip) {
        Log.d(TAG, "loadRecord called with id='" + id + "', skip=" + skip);
        this.prayId = id;
        Log.d(TAG, "prayId updated to: '" + this.prayId + "'");

        presenter.queryRecord(id, skip, size)
                .subscribe(this::onRecordSuccess, this::onBmobFailure);
        Log.d(TAG, "Query record request sent for id=" + id + ", skip=" + skip + ", size=" + size);
    }

    public void deleteRecord(int position) {
        HaoPray haoPray = recordList.get(position);
        presenter.deletePrayRecord(haoPray).subscribe(
                onDeleteSuccess(position)::accept, this::onBmobFailure);
    }

    public Consumer<String> onDeleteSuccess(int position) {
        Log.d(TAG, "onDeleteSuccess called for position: " + position);
        Log.d(TAG, "recordList size before removal: " + recordList.size());

        recordList.remove(position);
        Log.d(TAG, "recordList size after removal: " + recordList.size());

        recyclerView.notifyItemRemoved(position);
        Toast.makeText(getContext(), "删除成功", Toast.LENGTH_SHORT).show();

        // 删除后检查是否需要显示空状态
        updateEmptyState();

        return System.out::println;
    }

    private void onRecordSuccess(List<HaoPray> list) {
        Log.d(TAG, "onRecordSuccess called with " + (list != null ? list.size() : 0) + " records");
        Log.d(TAG, "Current prayId: '" + prayId + "'");

        if (list != null) {
            for (int i = 0; i < list.size(); i++) {
                HaoPray record = list.get(i);
                Log.d(TAG, "Record " + i + ": id=" + record.getId() + ", date=" + record.getDate() + ", pray=" + record.getPray());
            }
        }

        RefreshState state = refreshLayout.getState();
        Log.d(TAG, "RefreshLayout state: " + state);

        int oldSize = recordList.size();
        loadHelper.onSuccess(list, state == RefreshState.Loading, () -> {
            Log.d(TAG, "LoadHelper onSuccess callback - recordList size changed from " + oldSize + " to " + recordList.size());
            recyclerView.notifyDataSetChanged();
            refreshLayout.finishRefresh();
            refreshLayout.finishLoadMore();

            // 检查并显示/隐藏空状态视图
            updateEmptyState();
        });
    }

    public void onBmobFailure(Throwable e) {
        e.printStackTrace();
        refreshLayout.finishRefresh();
        refreshLayout.finishLoadMore();
        Toast.makeText(getContext(), e.getMessage(), Toast.LENGTH_SHORT).show();
    }

    public void scrollToTop() {
        recyclerView.scrollToPosition(0);
    }

    /**
     * 更新空状态视图的显示/隐藏
     */
    private void updateEmptyState() {
        boolean isEmpty = recordList.isEmpty();
        Log.d(TAG, "updateEmptyState called - recordList.isEmpty(): " + isEmpty + ", recordList.size(): " + recordList.size());
        Log.d(TAG, "emptyStateView is null: " + (emptyStateView == null));

        if (emptyStateView == null) {
            Log.e(TAG, "emptyStateView is null, cannot update empty state");
            return;
        }

        if (isEmpty) {
            Log.d(TAG, "Showing empty state");

            // 显示空状态视图
            ViewGroup parent = (ViewGroup) emptyStateView.getParent();
            Log.d(TAG, "emptyStateView parent: " + (parent != null ? parent.getClass().getSimpleName() : "null"));

            if (parent == null) {
                Log.d(TAG, "Adding emptyStateView to containerLayout");
                // 将空状态视图添加到容器布局中，覆盖在 SmartRefreshLayout 上方
                FrameLayout.LayoutParams params = new FrameLayout.LayoutParams(
                    FrameLayout.LayoutParams.MATCH_PARENT,
                    FrameLayout.LayoutParams.MATCH_PARENT
                );
                try {
                    containerLayout.addView(emptyStateView, params);
                    Log.d(TAG, "emptyStateView added to containerLayout successfully");
                } catch (Exception e) {
                    Log.e(TAG, "Failed to add emptyStateView to containerLayout", e);
                }
            } else {
                Log.d(TAG, "emptyStateView already has parent: " + parent.getClass().getSimpleName());
            }

            Log.d(TAG, "Setting emptyStateView visibility to VISIBLE");
            emptyStateView.setVisibility(View.VISIBLE);
            Log.d(TAG, "emptyStateView visibility after setting: " + emptyStateView.getVisibility());

            Log.d(TAG, "Setting recyclerView visibility to false");
            recyclerView.setVisible(false);

            // 在空状态下禁用加载更多，但保留刷新功能
            refreshLayout.setEnableLoadMore(false);
            refreshLayout.setEnableRefresh(true);
            Log.d(TAG, "Empty state configuration completed - LoadMore: false, Refresh: true");
        } else {
            Log.d(TAG, "Hiding empty state, showing data");

            // 隐藏空状态视图
            emptyStateView.setVisibility(View.GONE);
            Log.d(TAG, "emptyStateView visibility set to GONE");

            recyclerView.setVisible(true);
            Log.d(TAG, "recyclerView visibility set to true");

            // 有数据时启用刷新和加载更多功能
            refreshLayout.setEnableRefresh(true);
            refreshLayout.setEnableLoadMore(true);
            Log.d(TAG, "Data state configuration completed - LoadMore: true, Refresh: true");
        }

        Log.d(TAG, "updateEmptyState completed");
    }

    /**
     * 显示空状态（供外部调用）
     */
    public void showEmptyState() {
        Log.d(TAG, "showEmptyState called");
        Log.d(TAG, "Current recordList size before clear: " + recordList.size());
        Log.d(TAG, "Current prayId: '" + prayId + "'");

        recordList.clear();
        Log.d(TAG, "recordList cleared, new size: " + recordList.size());

        recyclerView.notifyDataSetChanged();
        Log.d(TAG, "RecyclerView notified of data change");

        updateEmptyState();

        // 在空状态下禁用加载更多，但保留刷新功能
        refreshLayout.setEnableLoadMore(false);
        refreshLayout.setEnableRefresh(true);
        Log.d(TAG, "showEmptyState completed - LoadMore: false, Refresh: true");
    }
}
