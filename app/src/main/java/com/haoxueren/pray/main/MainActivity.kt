package com.haoxueren.pray.main

import android.annotation.SuppressLint
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Bundle
import android.text.TextUtils
import android.view.View
import android.view.ViewGroup
import android.widget.EditText
import android.widget.LinearLayout
import android.widget.TextView
import android.widget.Toast
import androidx.compose.runtime.mutableStateOf
import androidx.compose.ui.platform.ComposeView
import com.haoxueren.base.BaseActivity
import com.haoxueren.pray.R
import com.haoxueren.pray.bean.HaoPray
import com.haoxueren.pray.manage.SettingsActivity
import com.haoxueren.pray.service.DataService
import com.haoxueren.sqlite.SQLiteHelper
import com.haoxueren.utils.DateUtils
import com.haoxueren.utils.ErrorUtils
import com.haoxueren.utils.MyPreferences
import com.haoxueren.utils.RxFingerPrint
import com.haoxueren.utils.ToastUtils
import com.haoxueren.view.StatefulLayout
import com.haoxueren.widget.PrayRecordWidget
import io.reactivex.disposables.Disposable

@SuppressLint("CheckResult")
class MainActivity : BaseActivity() {
    var rootLayout: ViewGroup? = null
    var toolbarComposeView: ComposeView? = null
    var prayLayout: LinearLayout? = null
    var dateEditText: TextView? = null
    var prayEditText: EditText? = null
    var countEditText: EditText? = null
    var stateLayout: StatefulLayout? = null
    var recordWidget: PrayRecordWidget? = null

    private var context: Context? = null
    private var currentPrayId: String? = null
    private var presenter: MainPresenter? = null
    private var isSavingHaoPray = false
    private val toolbarSubtitleState = mutableStateOf("目标培育计划")

    /**
     * 锁屏自动进入后台
     */
    private val screenReceiver: BroadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            val action = intent.action
            if (Intent.ACTION_SCREEN_OFF == action) {
                moveTaskToBack(true)
            }
        }
    }


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
    }

    override fun getLayoutResId(): Int {
        return R.layout.activity_main
    }

    override fun bindView(layout: View) {
        rootLayout = layout.findViewById(R.id.rootLayout)
        toolbarComposeView = layout.findViewById(R.id.toolbarComposeView)
        prayLayout = layout.findViewById(R.id.prayLayout)
        dateEditText = layout.findViewById(R.id.dateEditText)
        prayEditText = layout.findViewById(R.id.prayEditText)
        countEditText = layout.findViewById(R.id.countEditText)
        stateLayout = layout.findViewById(R.id.stateLayout)
        recordWidget = layout.findViewById(R.id.recordWidget)
    }

    override fun initView() {
        context = applicationContext

        // 设置 Compose Toolbar
        toolbarComposeView!!.setContent {
            MainToolbar(
                subtitle = toolbarSubtitleState.value,
                onNavigationClick = { /* 习惯功能已移除 */ },
                onRefreshClick = { onRefreshClick() },
                onPrayClick = { onPrayClick() },
                onDatabaseManageClick = { onDatabaseManageClick() },
                onDatabaseSyncClick = { onDatabaseSyncClick() },
                onSettingsClick = { SettingsActivity.start(this@MainActivity) }
            )
        }

        dateEditText!!.text = DateUtils.today()
        prayEditText!!.requestFocus()
        stateLayout!!.onLoading()
    }

    // 重构菜单点击处理为独立方法
    private fun onRefreshClick() {
        recordWidget!!.scrollToTop()
        prayEditText!!.setText("") // clear text
        presenter!!.randomId
            .doOnSubscribe { d: Disposable? -> stateLayout!!.onLoading(0) }
            .filter { id: String -> this.onEmpty(id) }
            .doOnNext { b: String? -> stateLayout!!.onSuccess() }
            .doOnError { e: Throwable? -> stateLayout!!.onFailure(e) }
            .subscribe(
                { randomId: String? -> this.onGroupSuccess(randomId) },
                { e: Throwable -> this.onBmobFailure(e) })
    }

    private fun onPrayClick() {
        if (isSavingHaoPray) {
            ToastUtils.showToast("接口请求中...")
            return
        }
        // id为空，先添加群组再添加记录
        if (currentPrayId == null) {
            val pray = prayEditText!!.text.toString().trim { it <= ' ' }
            if (TextUtils.isEmpty(pray)) {
                ToastUtils.showToast("内容不能为空！")
                return
            }
            currentPrayId = DateUtils.today("yyyyMMddHHmm")
            presenter!!.saveHaoGroup(0, currentPrayId, pray)
                .subscribe(
                    { messages: String? -> ToastUtils.showToast(messages) },
                    { t: Throwable? -> ErrorUtils.onError(t) })
        }
        isSavingHaoPray = true
        prayEditText!!.isEnabled = false
        val haoPray = HaoPray()
        haoPray.id = currentPrayId
        haoPray.setCount(countEditText!!.text.toString().trim { it <= ' ' })
        haoPray.date = dateEditText!!.text.toString().trim { it <= ' ' }
        haoPray.pray = prayEditText!!.text.toString().trim { it <= ' ' }
        presenter!!.saveHaoPray(haoPray)
            .subscribe({ pray: String? -> this.onPraySuccess(pray) }, { e: Throwable ->
                this.onBmobFailure(e)
                isSavingHaoPray = false
            })
    }

    private fun onDatabaseManageClick() {
        com.haoxueren.pray.manage.DatabaseManageActivity.start(this)
    }

    private fun onDatabaseSyncClick() {
        com.haoxueren.pray.sync.DatabaseSyncActivity.start(this)
    }

    override fun initData() {
        presenter = MainPresenter.getInstance()
        this.showFingerprintDialog() // 指纹验证通过才开始请求数据
        this.registerScreenOffReceiver() // 注册锁屏广播
    }

    private fun registerScreenOffReceiver() {
        val filter = IntentFilter()
        filter.addAction(Intent.ACTION_SCREEN_OFF)
        context!!.registerReceiver(screenReceiver, filter)
    }


    private fun showFingerprintDialog() {
        val preferences = MyPreferences.getInstance()
        if (preferences.isFingerprintEnabled) {
            // 如果启用了指纹验证，则执行指纹验证
            RxFingerPrint().authenticate(this).subscribe({ success: Boolean? ->
                if (!success!!) {
                    finish()
                    return@subscribe
                }
                // 指纹验证成功后执行数据加载
                proceedAfterAuthentication()
            }, { e: Throwable? -> finish() })
        } else {
            // 如果禁用了指纹验证，则直接执行数据加载
            proceedAfterAuthentication()
        }
    }

    // 将验证成功后的逻辑提取到一个新方法中，以便在跳过验证时也能执行
    private fun proceedAfterAuthentication() {
        presenter!!.updateTitle().subscribe { subtitle: String ->
            toolbarSubtitleState.value = subtitle
        }
        presenter!!.randomId
            .filter { id: String -> this.onEmpty(id) }
            .doOnNext { id: String? -> stateLayout!!.onSuccess() }
            .doOnError { e: Throwable? -> stateLayout!!.onFailure(e) }
            .subscribe(
                { randomId: String? -> this.onGroupSuccess(randomId) },
                { e: Throwable -> this.onBmobFailure(e) })
    }

    private fun onEmpty(id: String): Boolean {
        val empty = TextUtils.isEmpty(id)
        if (empty) {
            stateLayout!!.onEmpty()
        }
        return !empty
    }



    private fun onGroupSuccess(randomId: String?) {
        this.currentPrayId = randomId
        DataService.getInstance().getId2CountMap().subscribe { map: Map<String?, Int?> ->
            val count = map[randomId]
            prayEditText!!.isEnabled = true
            if (count == null || count == 0) {
                // 空分组的情况
                countEditText!!.setText("0")
                prayEditText!!.hint = randomId
                // 显示空状态界面
                recordWidget!!.showEmptyState()
            } else {
                // 有记录的分组
                prayEditText!!.hint = ""
                countEditText!!.setText(count.toString() + "")
                recordWidget!!.loadRecord(randomId, 0)
            }
        }
    }


    private fun onPraySuccess(pray: String?) {
        isSavingHaoPray = false
        prayEditText!!.setText("")
        prayEditText!!.hint = pray
        prayEditText!!.isEnabled = false
        recordWidget!!.loadRecord(currentPrayId, 0)
        presenter!!.updateTitle().subscribe { subtitle: String -> 
            toolbarSubtitleState.value = subtitle 
        }
    }

    private fun onBmobFailure(e: Throwable) {
        e.printStackTrace()
        ToastUtils.showToast(e.message)
    }

    private var firstPressedTime: Long = 0

    override fun onBackPressed() {
        val nextPressedTime = System.currentTimeMillis()
        if (nextPressedTime - firstPressedTime < 2000) {
            super.onBackPressed()
            finishAndRemoveTask() // 华为手机返回时未关闭Activity
        } else {
            firstPressedTime = nextPressedTime
            Toast.makeText(context, "再按一次退出", Toast.LENGTH_SHORT).show()
        }
    }


    override fun onDestroy() {
        super.onDestroy()
        DataService.getInstance().clear()
        context!!.unregisterReceiver(screenReceiver)
        SQLiteHelper.getInstance().close()
    }

    companion object {
        @JvmStatic
        fun start(context: Context) {
            val intent = Intent(context, MainActivity::class.java)
            context.startActivity(intent)
        }
    }
}
