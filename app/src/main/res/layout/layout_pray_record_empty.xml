<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center"
    android:orientation="vertical"
    android:padding="@dimen/padding_default"
    android:background="@color/theme">

    <!-- 空状态图标 -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="🙏"
        android:textSize="64sp"
        android:layout_marginBottom="16dp" />

    <!-- 主要提示文字 -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="暂无祈祷记录"
        android:textColor="@color/tc_primary"
        android:textSize="18sp"
        android:textStyle="bold"
        android:layout_marginBottom="8dp" />

    <!-- 副标题提示 -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="点击上方的祈祷按钮开始记录您的祈祷"
        android:textColor="@color/tc_secondary"
        android:textSize="14sp"
        android:gravity="center"
        android:layout_marginBottom="16dp" />

    <!-- 引导性提示 -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="💡 小贴士：每次祈祷都会被记录下来，帮助您追踪祈祷的历程"
        android:textColor="@color/tc_secondary"
        android:textSize="12sp"
        android:gravity="center"
        android:layout_marginTop="8dp"
        android:padding="12dp"
        android:background="@drawable/shape_bg_border"
        android:layout_marginHorizontal="16dp" />

</LinearLayout>
